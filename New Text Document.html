<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Qu<PERSON><PERSON> lý t<PERSON> | NGUYEN VAN DUY</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@1/css/pico.min.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    :root {
      --primary-blue: #1e40af;
      --primary-red: #dc2626;
      --light-blue: #dbeafe;
      --light-red: #fee2e2;
      --dark-blue: #1e3a8a;
      --dark-red: #991b1b;
      --white: #ffffff;
      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    * {
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, var(--gray-50) 0%, var(--light-blue) 100%);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      min-height: 100vh;
    }

    /* Sidebar Styles */
    aside {
      min-width: 280px;
      background: linear-gradient(180deg, var(--primary-blue) 0%, var(--dark-blue) 100%);
      min-height: 100vh;
      padding: 0;
      position: relative;
      box-shadow: var(--shadow-xl);
    }

    .sidebar-header {
      padding: 2rem 1.5rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(255, 255, 255, 0.05);
    }

    .sidebar-logo {
      font-size: 1.8rem;
      font-weight: 800;
      color: var(--white);
      text-align: center;
      letter-spacing: 3px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .sidebar-subtitle {
      text-align: center;
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.85rem;
      margin-top: 0.5rem;
      letter-spacing: 1px;
    }

    nav.sidebar-nav {
      padding: 1.5rem 0;
    }

    nav.sidebar-nav ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    nav.sidebar-nav li {
      margin-bottom: 0.5rem;
    }

    nav.sidebar-nav a {
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      font-size: 1rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem 1.5rem;
      transition: all 0.3s ease;
      border-radius: 0;
      position: relative;
    }

    nav.sidebar-nav a:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--white);
      transform: translateX(5px);
    }

    nav.sidebar-nav a.active {
      background: linear-gradient(90deg, var(--primary-red), var(--dark-red));
      color: var(--white);
      font-weight: 600;
      box-shadow: var(--shadow-md);
    }

    nav.sidebar-nav a.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: var(--white);
    }

    nav.sidebar-nav i {
      width: 20px;
      text-align: center;
      font-size: 1.1rem;
    }

    .sidebar-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 1.5rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(0, 0, 0, 0.1);
    }

    /* Main Content Styles */
    .main-wrapper {
      flex: 1;
      padding: 2rem;
      overflow-y: auto;
    }

    .page-header {
      background: var(--white);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--gray-200);
    }

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
      font-size: 0.9rem;
      color: var(--gray-500);
    }

    .breadcrumb a {
      color: var(--primary-blue);
      text-decoration: none;
    }

    .breadcrumb a:hover {
      text-decoration: underline;
    }

    .page-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .page-title i {
      color: var(--primary-blue);
    }

    /* Profile Section */
    .profile-section {
      background: var(--white);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--gray-200);
    }

    .profile-header {
      display: flex;
      align-items: center;
      gap: 2rem;
      margin-bottom: 2rem;
      padding-bottom: 2rem;
      border-bottom: 2px solid var(--gray-100);
    }

    .avatar-container {
      position: relative;
    }

    .avatar {
      border-radius: 50%;
      width: 80px;
      height: 80px;
      object-fit: cover;
      border: 4px solid var(--primary-blue);
      box-shadow: var(--shadow-md);
    }

    .avatar-badge {
      position: absolute;
      bottom: 0;
      right: 0;
      background: var(--primary-red);
      color: var(--white);
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      border: 3px solid var(--white);
    }

    .profile-info h2 {
      font-size: 1.8rem;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 0.5rem 0;
    }

    .profile-info .account-id {
      font-size: 1rem;
      color: var(--gray-500);
      font-weight: 500;
      margin-bottom: 1rem;
    }

    .status-badges {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: 50px;
      font-weight: 600;
      font-size: 0.85rem;
      letter-spacing: 0.5px;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .status-badge.active {
      background: var(--light-blue);
      color: var(--primary-blue);
      border: 1px solid var(--primary-blue);
    }

    .status-badge.verified {
      background: var(--light-red);
      color: var(--primary-red);
      border: 1px solid var(--primary-red);
    }

    /* Form Styles */
    .form-section {
      background: var(--white);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--gray-200);
    }

    .form-section h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 1.5rem 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .form-section h3 i {
      color: var(--primary-blue);
    }

    .form-grid {
      display: grid;
      gap: 1.5rem;
      grid-template-columns: 1fr;
    }

    @media (min-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr 1fr;
      }
      .form-grid .full-width {
        grid-column: 1 / -1;
      }
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 600;
      color: var(--gray-700);
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .form-group label .required {
      color: var(--primary-red);
    }

    .form-group input,
    .form-group select {
      padding: 0.75rem 1rem;
      border: 2px solid var(--gray-200);
      border-radius: 8px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: var(--white);
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: var(--primary-blue);
      box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    }

    .form-group input[readonly] {
      background: var(--gray-50);
      color: var(--gray-500);
      cursor: not-allowed;
    }

    /* Meta Info */
    .meta-section {
      background: var(--gray-50);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--gray-200);
    }

    .meta-grid {
      display: grid;
      gap: 1rem;
      grid-template-columns: 1fr;
    }

    @media (min-width: 768px) {
      .meta-grid {
        grid-template-columns: 1fr 1fr;
      }
    }

    .meta-item {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .meta-label {
      font-size: 0.85rem;
      color: var(--gray-500);
      font-weight: 500;
    }

    .meta-value {
      font-size: 0.95rem;
      color: var(--gray-900);
      font-weight: 600;
    }

    /* Action Buttons */
    .action-section {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      flex-wrap: wrap;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
      min-width: 120px;
      justify-content: center;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
      color: var(--white);
      box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--primary-red), var(--dark-red));
      color: var(--white);
      box-shadow: var(--shadow-md);
    }

    .btn-danger:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .btn-secondary {
      background: var(--white);
      color: var(--gray-700);
      border: 2px solid var(--gray-300);
    }

    .btn-secondary:hover {
      background: var(--gray-50);
      border-color: var(--gray-400);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      aside {
        min-width: 70px;
        padding: 0;
      }

      .sidebar-header {
        padding: 1rem 0.5rem;
      }

      .sidebar-logo {
        font-size: 1.2rem;
        letter-spacing: 1px;
      }

      .sidebar-subtitle {
        display: none;
      }

      nav.sidebar-nav a {
        padding: 1rem 0.5rem;
        justify-content: center;
      }

      nav.sidebar-nav a span {
        display: none;
      }

      .main-wrapper {
        padding: 1rem;
      }

      .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
      }

      .action-section {
        justify-content: stretch;
      }

      .btn {
        flex: 1;
        min-width: auto;
      }
    }

    /* Loading Animation */
    .loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid var(--primary-blue);
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  </style>
</head>
<body>
  <div style="display: flex; min-height: 100vh;">
    <!-- Sidebar -->
    <aside>
      <div class="sidebar-header">
        <div class="sidebar-logo">QTTK</div>
        <div class="sidebar-subtitle">Quản Trị Hệ Thống</div>
      </div>

      <nav class="sidebar-nav">
        <ul>
          <li><a href="#"><i class="fas fa-clipboard-list"></i> <span>Quản lý log</span></a></li>
          <li><a href="#" class="active"><i class="fas fa-user-cog"></i> <span>Quản lý tài khoản</span></a></li>
          <li><a href="#"><i class="fas fa-comments"></i> <span>Quản lý tin nhắn</span></a></li>
          <li><a href="#"><i class="fas fa-users"></i> <span>Quản lý khách hàng</span></a></li>
          <li><a href="#"><i class="fas fa-chart-pie"></i> <span>Dashboard</span></a></li>
          <li><a href="#"><i class="fas fa-chart-line"></i> <span>Báo cáo thống kê</span></a></li>
          <li><a href="#"><i class="fas fa-cog"></i> <span>Cài đặt</span></a></li>
        </ul>
      </nav>

      <div class="sidebar-footer">
        <a href="#" style="color: rgba(255, 255, 255, 0.9); text-decoration: none; display: flex; align-items: center; gap: 1rem; padding: 0.5rem;">
          <i class="fas fa-sign-out-alt"></i>
          <span>Đăng xuất</span>
        </a>
      </div>
    </aside>

    <!-- Main Content -->
    <div class="main-wrapper">
      <!-- Page Header -->
      <div class="page-header">
        <div class="breadcrumb">
          <a href="#"><i class="fas fa-home"></i> Trang chủ</a>
          <i class="fas fa-chevron-right"></i>
          <span>Quản lý tài khoản</span>
        </div>
        <h1 class="page-title">
          <i class="fas fa-user-edit"></i>
          Quản lý tài khoản
        </h1>
      </div>

      <!-- Profile Section -->
      <div class="profile-section">
        <div class="profile-header">
          <div class="avatar-container">
            <img class="avatar" src="https://images.unsplash.com/photo-*************-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&facepad=2" alt="Avatar Người dùng" />
            <div class="avatar-badge">
              <i class="fas fa-check"></i>
            </div>
          </div>
          <div class="profile-info">
            <h2>NGUYEN VAN DUY</h2>
            <div class="account-id">#********</div>
            <div class="status-badges">
              <span class="status-badge active">
                <i class="fas fa-check-circle"></i>
                Tài khoản hiệu lực
              </span>
              <span class="status-badge verified">
                <i class="fas fa-shield-alt"></i>
                Đã xác thực
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Section -->
      <div class="form-section">
        <h3><i class="fas fa-edit"></i> Thông tin tài khoản</h3>
        <form>
          <div class="form-grid">
            <div class="form-group">
              <label for="username">
                <i class="fas fa-user"></i>
                Tên đăng nhập <span class="required">*</span>
              </label>
              <input type="text" id="username" name="username" value="********" readonly>
            </div>
            <div class="form-group">
              <label for="fullname">
                <i class="fas fa-id-card"></i>
                Họ và tên <span class="required">*</span>
              </label>
              <input type="text" id="fullname" name="fullname" value="NGUYEN VAN DUY" required>
            </div>
            <div class="form-group">
              <label for="email">
                <i class="fas fa-envelope"></i>
                Email <span class="required">*</span>
              </label>
              <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
              <label for="unit">
                <i class="fas fa-building"></i>
                Đơn vị quản lý <span class="required">*</span>
              </label>
              <select id="unit" name="unit" required>
                <option selected>HA DONG T/O</option>
                <option>Đơn vị khác</option>
              </select>
            </div>
            <div class="form-group full-width">
              <label for="user-action">
                <i class="fas fa-tasks"></i>
                Yêu cầu người dùng khi đăng nhập
              </label>
              <select id="user-action" name="user-action">
                <option>Chọn hành động của người dùng khi đăng nhập</option>
                <option>Không yêu cầu</option>
                <option>Yêu cầu đổi mật khẩu</option>
                <option>Yêu cầu xác thực OTP</option>
              </select>
            </div>
          </div>
        </form>
      </div>

      <!-- Meta Information -->
      <div class="meta-section">
        <div class="meta-grid">
          <div class="meta-item">
            <div class="meta-label">Người tạo</div>
            <div class="meta-value">NGUYEN NGOC BAO TRAM</div>
          </div>
          <div class="meta-item">
            <div class="meta-label">Ngày tạo</div>
            <div class="meta-value">15/08/2024 15:39:00</div>
          </div>
          <div class="meta-item">
            <div class="meta-label">Người cập nhật gần nhất</div>
            <div class="meta-value">NGUYEN NGOC BAO TRAM</div>
          </div>
          <div class="meta-item">
            <div class="meta-label">Ngày cập nhật gần nhất</div>
            <div class="meta-value">15/08/2024 15:39:00</div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-section">
        <button type="button" class="btn btn-primary">
          <i class="fas fa-save"></i>
          Lưu thay đổi
        </button>
        <button type="button" class="btn btn-danger">
          <i class="fas fa-trash"></i>
          Xóa tài khoản
        </button>
        <button type="button" class="btn btn-secondary">
          <i class="fas fa-times"></i>
          Hủy bỏ
        </button>
      </div>
    </div>
  </div>

  <script>
    // Add some interactive functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Form validation
      const form = document.querySelector('form');
      const inputs = form.querySelectorAll('input[required], select[required]');

      inputs.forEach(input => {
        input.addEventListener('blur', function() {
          if (!this.value.trim()) {
            this.style.borderColor = 'var(--primary-red)';
          } else {
            this.style.borderColor = 'var(--gray-200)';
          }
        });
      });

      // Button loading states
      const buttons = document.querySelectorAll('.btn');
      buttons.forEach(button => {
        button.addEventListener('click', function() {
          if (!this.classList.contains('btn-secondary')) {
            this.classList.add('loading');
            setTimeout(() => {
              this.classList.remove('loading');
            }, 2000);
          }
        });
      });

      // Sidebar navigation
      const navLinks = document.querySelectorAll('nav.sidebar-nav a');
      navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          navLinks.forEach(l => l.classList.remove('active'));
          this.classList.add('active');
        });
      });
    });
  </script>
</body>
</html>
