<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><PERSON>u<PERSON>n lý t<PERSON> | NGUYEN VAN DUY</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@1/css/pico.min.css">
  <style>
    body {
      background: #f8fafc;
    }
    aside {
      min-width: 210px;
      background: #ffffff;
      border-right: 1px solid #e5e7eb;
      min-height: 100vh;
      padding: 2rem 1rem;
    }
    .sidebar-logo {
      font-size: 1.4rem;
      font-weight: bold;
      margin-bottom: 2rem;
      color: #2584ff;
      letter-spacing: 2px;
      text-align: center;
    }
    nav.sidebar-nav ul {
      list-style: none;
      padding: 0;
      margin: 1rem 0;
    }
    nav.sidebar-nav li {
      margin-bottom: 1.2rem;
    }
    nav.sidebar-nav a, nav.sidebar-nav span {
      color: #1e293b;
      text-decoration: none;
      font-size: 1.04rem;
      display: flex;
      align-items: center;
      gap: 0.7em;
      transition: color 0.2s;
    }
    nav.sidebar-nav a.active, nav.sidebar-nav a:hover {
      color: #2584ff;
      font-weight: 600;
    }
    .profile-header {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      margin-bottom: 2.2rem;
      justify-content: space-between;
    }
    .avatar {
      border-radius: 50%;
      width: 66px;
      height: 66px;
      object-fit: cover;
      border: 3px solid #e5e7eb;
    }
    .status-badge {
      background: #e8f7ff;
      color: #2584ff;
      padding: 0.3em 0.9em;
      border-radius: 99px;
      font-weight: 600;
      font-size: 0.98em;
      letter-spacing: 1px;
    }
    .account-title {
      font-size: 1.6rem;
      font-weight: 700;
      color: #2584ff;
      margin: 0 0 0.3em 0;
      letter-spacing: 1px;
    }
    .account-id {
      font-size: 1.08rem;
      color: #6b7280;
      font-weight: 500;
      margin-bottom: 0.2em;
    }
    form.grid {
      gap: 1.3rem;
      background: #fff;
      border-radius: 12px;
      padding: 2rem 2.2rem;
      box-shadow: 0 3px 20px rgba(0,0,0,0.04);
      margin-bottom: 2.4rem;
    }
    @media (min-width: 900px) {
      main .grid {
        grid-template-columns: 1fr 1fr;
        gap: 2.6rem;
      }
      form.grid {
        grid-template-columns: 1fr 1fr;
      }
    }
    label {
      font-weight: 500;
      margin-bottom: 0.4em;
    }
    .action-btns {
      display: flex;
      gap: 1.2em;
      justify-content: flex-end;
      margin-top: 1.2em;
    }
    .action-btns button {
      min-width: 110px;
      font-size: 1.09em;
      display: flex;
      align-items: center;
      gap: 0.4em;
      padding: 0.48em 1.1em;
    }
    .meta-info {
      font-size: 1em;
      color: #6b7280;
      display: flex;
      gap: 2.6em;
      margin-bottom: 0.5em;
    }
    @media (max-width: 700px) {
      aside {
        min-width: 100px;
        padding: 1rem 0.3rem;
      }
      .profile-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.7em;
      }
      .meta-info {
        flex-direction: column;
        gap: 0.3em;
      }
      form.grid {
        padding: 1.1rem 0.6rem;
      }
    }
  </style>
</head>
<body>
  <div style="display: flex; min-height: 100vh;">
    <!-- Sidebar -->
    <aside>
      <div class="sidebar-logo">
        <span>QTTK</span>
      </div>
      <nav class="sidebar-nav">
        <ul>
          <li><a href="#"><span>📋</span> Quản lý log</a></li>
          <li><a href="#" class="active"><span>👤</span> Quản lý tài khoản</a></li>
          <li><a href="#"><span>💬</span> Quản lý tin nhắn</a></li>
          <li><a href="#"><span>👥</span> Quản lý khách hàng</a></li>
          <li><a href="#"><span>📊</span> Dashboard</a></li>
          <li><a href="#"><span>📈</span> Báo cáo thống kê</a></li>
          <li><a href="#"><span>⚙️</span> Cài đặt</a></li>
        </ul>
        <ul>
          <li><a href="#"><span>🔒</span> Đăng xuất</a></li>
        </ul>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="container" style="max-width: 900px; padding-top: 2rem;">
      <div class="profile-header">
        <div>
          <div class="account-id">#********</div>
          <div class="account-title">NGUYEN VAN DUY</div>
        </div>
        <div>
          <img class="avatar" src="https://images.unsplash.com/photo-*************-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&facepad=2" alt="Avatar Người dùng" />
        </div>
      </div>

      <div style="display:flex; align-items:center; gap:1em; margin-bottom:2em;">
        <span class="status-badge">Hiệu lực</span>
      </div>

      <form class="grid">
        <div>
          <label for="username">Tên đăng nhập *</label>
          <input type="text" id="username" name="username" value="********" readonly>
        </div>
        <div>
          <label for="fullname">Họ và tên *</label>
          <input type="text" id="fullname" name="fullname" value="NGUYEN VAN DUY" required>
        </div>
        <div>
          <label for="email">Email *</label>
          <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        <div>
          <label for="unit">Đơn vị quản lý *</label>
          <select id="unit" name="unit" required>
            <option selected>HA DONG T/O</option>
            <option>Đơn vị khác</option>
          </select>
        </div>
        <div style="grid-column: 1 / -1;">
          <label for="user-action">Yêu cầu người dùng khi đăng nhập</label>
          <select id="user-action" name="user-action">
            <option>Chọn hành động của người dùng khi đăng nhập</option>
            <option>Không yêu cầu</option>
            <option>Yêu cầu đổi mật khẩu</option>
            <option>Yêu cầu xác thực OTP</option>
          </select>
        </div>
      </form>

      <div class="meta-info">
        <div>Người tạo: <strong>NGUYEN NGOC BAO TRAM</strong></div>
        <div>Ngày tạo: <strong>15/08/2024 15:39:00</strong></div>
        <div>Người cập nhật gần nhất: <strong>NGUYEN NGOC BAO TRAM</strong></div>
        <div>Ngày cập nhật gần nhất: <strong>15/08/2024 15:39:00</strong></div>
      </div>

      <div class="action-btns">
        <button type="button" style="background:#2584ff; color:#fff;"><span>💾</span> Lưu</button>
        <button type="button" class="secondary"><span>❌</span> Hủy</button>
      </div>
    </main>
  </div>
</body>
</html>
